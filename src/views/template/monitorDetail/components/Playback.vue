<script setup lang="ts">
import type { RecordItem } from '@/api/monitor.ts'
import dayjs from 'dayjs'
import { MonitorAPI } from '@/api/monitor.ts'

const { deviceId, channelId } = defineProps<{
  deviceId: string
  channelId: string
}>()

const tableList = ref<RecordItem[]>([])
const loading = ref(false)
function handleQuery() {
  loading.value = true
  MonitorAPI.deviceRecord(deviceId, channelId, '2024-01-01 00:00:00', '2026-01-02 00:00:00').then((res) => {
    console.log(res)
    tableList.value = res.recordList
  }).finally(() => {
    loading.value = false
  })
}

handleQuery()
</script>

<template>
  <div class="flex flex-col">
    <header class="flex items-center gap-5 h-22 mb-16">
      <i class="i-temp-monitor" text="16 primary" />
      <span>录像片段</span>
      <span>（22个）</span>
    </header>
    <el-scrollbar>
      <div class="flex flex-col gap-10">
        <div v-for="item in tableList" :key="item.filePath" class="rounded-4" p="x-16 y-12" b="1 solid border">
          <div>
            <span>{{ dayjs(item.startTime).format('HH:mm:ss') }} - {{ dayjs(item.endTime).format('HH:mm:ss') }}</span>
            <span>{{ dayjs(item.startTime).diff(dayjs(item.endTime)) }}</span>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<style scoped lang="scss">

</style>
